set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250611_120001-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-11 12:00:01.684760
Config  : ./p1.site/private/backups/20250611_120001-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250611_120001-p1_site-database.sql.gz         234.0KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250611_180001-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-11 18:00:01.837521
Config  : ./p1.site/private/backups/20250611_180001-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250611_180001-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250612_120001-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-12 12:00:02.314550
Config  : ./p1.site/private/backups/20250612_120001-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250612_120001-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250612_180002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-12 18:00:02.689098
Config  : ./p1.site/private/backups/20250612_180002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250612_180002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250613_000002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-13 00:00:03.475764
Config  : ./p1.site/private/backups/20250613_000002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250613_000002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250613_120002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-13 12:00:03.181886
Config  : ./p1.site/private/backups/20250613_120002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250613_120002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250613_180002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-13 18:00:02.952448
Config  : ./p1.site/private/backups/20250613_180002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250613_180002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250615_120001-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-15 12:00:02.251310
Config  : ./p1.site/private/backups/20250615_120001-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250615_120001-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250615_180002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-15 18:00:02.885883
Config  : ./p1.site/private/backups/20250615_180002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250615_180002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250616_120003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-16 12:00:04.361184
Config  : ./p1.site/private/backups/20250616_120003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250616_120003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250616_180002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-16 18:00:03.988928
Config  : ./p1.site/private/backups/20250616_180002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250616_180002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250617_120003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-17 12:00:04.320733
Config  : ./p1.site/private/backups/20250617_120003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250617_120003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250617_180002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-17 18:00:03.660521
Config  : ./p1.site/private/backups/20250617_180002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250617_180002-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250618_120003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-18 12:00:04.397865
Config  : ./p1.site/private/backups/20250618_120003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250618_120003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250618_180003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-18 18:00:04.713940
Config  : ./p1.site/private/backups/20250618_180003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250618_180003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250619_120003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-19 12:00:04.573280
Config  : ./p1.site/private/backups/20250619_120003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250619_120003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250619_180003-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-19 18:00:04.023444
Config  : ./p1.site/private/backups/20250619_180003-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250619_180003-p1_site-database.sql.gz         239.3KiB
Backup for Site p1.site has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_f27ad63c43a8745e --host=localhost --port=3306 --password=********** --single-transaction --quick --lock-tables=false _f27ad63c43a8745e | /usr/bin/gzip >> ./p1.site/private/backups/20250620_120002-p1_site-database.sql.gz

Backup Summary for p1.site at 2025-06-20 12:00:02.819599
Config  : ./p1.site/private/backups/20250620_120002-p1_site-site_config_backup.json 230.0B
Database: ./p1.site/private/backups/20250620_120002-p1_site-database.sql.gz         246.7KiB
Backup for Site p1.site has been successfully completed
