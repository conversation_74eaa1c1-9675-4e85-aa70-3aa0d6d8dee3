// DMSoko API Service Layer
// Centralized API calls for the Vue.js frontend

class ApiService {
  constructor() {
    this.baseUrl = '/api/method/dmsoko.api';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Frappe-CSRF-Token': window.frappe?.session?.csrf_token || ''
    };
  }

  // Generic API call method
  async call(endpoint, data = {}, method = 'POST') {
    try {
      const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}.${endpoint}`;
      
      const options = {
        method,
        headers: { ...this.defaultHeaders }
      };

      if (method === 'POST' && Object.keys(data).length > 0) {
        options.body = JSON.stringify(data);
      } else if (method === 'GET' && Object.keys(data).length > 0) {
        const params = new URLSearchParams(data);
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error(`API call failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Frappe-style API call (backward compatibility)
  async frappeCall(method, args = {}) {
    try {
      const response = await window.frappe.call({
        method,
        args
      });
      return response;
    } catch (error) {
      console.error(`Frappe call failed for ${method}:`, error);
      throw error;
    }
  }

  // ============ LISTINGS API ============
  
  async getListings(filters = {}, page = 1, pageSize = 20, sortBy = 'creation', sortOrder = 'desc') {
    return this.frappeCall('dmsoko.api.listings.get_listings', {
      filters: JSON.stringify(filters),
      page,
      page_size: pageSize,
      sort_by: sortBy,
      sort_order: sortOrder
    });
  }

  async getListing(listingId) {
    return this.frappeCall('dmsoko.api.listings.get_listing', {
      listing_id: listingId
    });
  }

  async createListing(listingData) {
    return this.frappeCall('dmsoko.api.listings.create_listing', listingData);
  }

  async updateListing(listingId, listingData) {
    return this.frappeCall('dmsoko.api.listings.update_listing', {
      listing_id: listingId,
      ...listingData
    });
  }

  async deleteListing(listingId) {
    return this.frappeCall('dmsoko.api.listings.delete_listing', {
      listing_id: listingId
    });
  }

  async getFeaturedListings(limit = 6) {
    return this.frappeCall('dmsoko.api.listings.get_featured_listings', {
      limit
    });
  }

  async getRecentListings(limit = 8) {
    return this.frappeCall('dmsoko.api.listings.get_recent_listings', {
      limit
    });
  }

  async getUserListings(user = null, status = null, page = 1, pageSize = 20) {
    return this.frappeCall('dmsoko.api.listings.get_user_listings', {
      user,
      status,
      page,
      page_size: pageSize
    });
  }

  // ============ CATEGORIES API ============
  
  async getCategories() {
    return this.frappeCall('dmsoko.api.categories.get_categories');
  }

  async getPopularCategories(limit = 10) {
    return this.frappeCall('dmsoko.api.categories.get_popular_categories', {
      limit
    });
  }

  async getCategoryTree() {
    return this.frappeCall('dmsoko.api.categories.get_category_tree');
  }

  // ============ SEARCH API ============
  
  async searchListings(query, filters = {}, page = 1, pageSize = 20) {
    return this.frappeCall('dmsoko.api.search.search_listings', {
      query,
      filters: JSON.stringify(filters),
      page,
      page_size: pageSize
    });
  }

  async getSearchFilters() {
    return this.frappeCall('dmsoko.api.search.get_search_filters');
  }

  async getSearchSuggestions(query) {
    return this.frappeCall('dmsoko.api.search.get_search_suggestions', {
      query
    });
  }

  // ============ USER API ============
  
  async getUserProfile(userId = null) {
    return this.frappeCall('dmsoko.api.users.get_user_profile', {
      user_id: userId
    });
  }

  async updateUserProfile(profileData) {
    return this.frappeCall('dmsoko.api.users.update_user_profile', profileData);
  }

  async getUserStats(userId = null) {
    return this.frappeCall('dmsoko.api.users.get_user_stats', {
      user_id: userId
    });
  }

  // ============ WISHLIST API ============
  
  async getWishlist() {
    return this.frappeCall('dmsoko.api.wishlist.get_wishlist');
  }

  async addToWishlist(listingId) {
    return this.frappeCall('dmsoko.api.wishlist.add_to_wishlist', {
      listing_id: listingId
    });
  }

  async removeFromWishlist(listingId) {
    return this.frappeCall('dmsoko.api.wishlist.remove_from_wishlist', {
      listing_id: listingId
    });
  }

  // ============ MESSAGES API ============
  
  async getConversations() {
    return this.frappeCall('dmsoko.api.messages.get_conversations');
  }

  async getMessages(conversationId) {
    return this.frappeCall('dmsoko.api.messages.get_messages', {
      conversation_id: conversationId
    });
  }

  async sendMessage(conversationId, message) {
    return this.frappeCall('dmsoko.api.messages.send_message', {
      conversation_id: conversationId,
      message
    });
  }

  async createConversation(listingId, message) {
    return this.frappeCall('dmsoko.api.messages.create_conversation', {
      listing_id: listingId,
      message
    });
  }

  // ============ AUTHENTICATION API ============
  
  async login(email, password) {
    return this.frappeCall('login', {
      usr: email,
      pwd: password
    });
  }

  async logout() {
    return this.frappeCall('logout');
  }

  async register(userData) {
    return this.frappeCall('dmsoko.api.users.register_user', userData);
  }

  async resetPassword(email) {
    return this.frappeCall('frappe.core.doctype.user.user.reset_password', {
      user: email
    });
  }

  // ============ FILE UPLOAD API ============
  
  async uploadFile(file, doctype = 'Listing', docname = null) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('doctype', doctype);
    if (docname) formData.append('docname', docname);

    try {
      const response = await fetch('/api/method/upload_file', {
        method: 'POST',
        headers: {
          'X-Frappe-CSRF-Token': window.frappe?.session?.csrf_token || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }

  // ============ UTILITY METHODS ============
  
  formatPrice(price, currency = 'TZS') {
    if (!price) return 'Price on request';
    return new Intl.NumberFormat('en-TZ', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0
    }).format(price);
  }

  formatDate(dateStr) {
    return new Date(dateStr).toLocaleDateString();
  }

  formatRelativeTime(dateStr) {
    const date = new Date(dateStr);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return this.formatDate(dateStr);
  }
}

// Create singleton instance
const apiService = new ApiService();

// Export for use in Vue components
window.apiService = apiService;

export default apiService;
