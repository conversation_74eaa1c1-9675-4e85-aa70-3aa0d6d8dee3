// DMSoko Vue.js Application
// Professional Classifieds Platform Frontend
// Using CDN libraries (Vue 3, Vue Router 4, Pinia)

// Wait for all CDN libraries to load
window.addEventListener('DOMContentLoaded', function() {
  // Check if Vue and other libraries are loaded
  if (typeof Vue === 'undefined' || typeof VueRouter === 'undefined' || typeof Pinia === 'undefined') {
    console.error('Required libraries not loaded');
    return;
  }

  const { createApp, ref, computed, onMounted, watch } = Vue;
  const { createRouter, createWebHistory } = VueRouter;
  const { createPinia, defineStore } = Pinia;

  // Import API service
  if (typeof window.apiService === 'undefined') {
    // Load API service if not already loaded
    const script = document.createElement('script');
    script.src = '/assets/dmsoko/js/vue-app/services/api.js';
    script.type = 'module';
    document.head.appendChild(script);
  }

  // Listings component
  const Listings = {
    template: `
      <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Browse Listings</h1>

        <div v-if="loading" class="text-center py-8">
          <p class="text-gray-600">Loading listings...</p>
        </div>
        <div v-else-if="listings.length === 0" class="text-center py-8">
          <p class="text-gray-600">No listings found.</p>
        </div>
        <div v-else>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="listing in listings" :key="listing.name" class="bg-white rounded-lg shadow-md overflow-hidden">
              <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ listing.title }}</h3>
                <p class="text-gray-600 mb-3 line-clamp-3">{{ listing.description }}</p>
                <div class="flex justify-between items-center mb-3">
                  <span class="text-xl font-bold text-blue-600">{{ formatPrice(listing.price) }}</span>
                  <span class="text-sm text-gray-500">{{ listing.location }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ listing.category_name || listing.category }}</span>
                  <span class="text-xs text-gray-500">{{ formatDate(listing.creation) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Load More Button -->
          <div v-if="hasMore" class="text-center mt-8">
            <button @click="loadMore" :disabled="loadingMore" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50">
              {{ loadingMore ? 'Loading...' : 'Load More' }}
            </button>
          </div>
        </div>
      </div>
    `,
    data() {
      return {
        listings: [],
        loading: true,
        loadingMore: false,
        page: 1,
        hasMore: true
      }
    },
    async mounted() {
      await this.loadListings();
    },
    methods: {
      async loadListings() {
        try {
          const response = await window.frappe.call({
            method: 'dmsoko.api.listings.get_listings',
            args: {
              page: this.page,
              page_size: 12
            }
          });

          if (response.message && response.message.success) {
            const data = response.message.data;
            if (this.page === 1) {
              this.listings = data.listings || [];
            } else {
              this.listings.push(...(data.listings || []));
            }
            this.hasMore = this.page < data.total_pages;
          } else {
            // Fallback to get_all_listings
            const fallbackResponse = await window.frappe.call({
              method: 'dmsoko.api.listings.get_all_listings'
            });
            if (fallbackResponse.message && fallbackResponse.message.success) {
              this.listings = fallbackResponse.message.data || [];
              this.hasMore = false;
            }
          }
        } catch (error) {
          console.error('Failed to load listings:', error);
          // Try fallback API
          try {
            const fallbackResponse = await window.frappe.call({
              method: 'dmsoko.api.listings.get_all_listings'
            });
            if (fallbackResponse.message && fallbackResponse.message.success) {
              this.listings = fallbackResponse.message.data || [];
              this.hasMore = false;
            }
          } catch (fallbackError) {
            console.error('Fallback API also failed:', fallbackError);
          }
        } finally {
          this.loading = false;
          this.loadingMore = false;
        }
      },
      async loadMore() {
        this.loadingMore = true;
        this.page++;
        await this.loadListings();
      },
      formatPrice(price) {
        if (!price) return 'Price on request';
        return new Intl.NumberFormat('en-TZ', {
          style: 'currency',
          currency: 'TZS',
          minimumFractionDigits: 0
        }).format(price);
      },
      formatDate(dateStr) {
        return new Date(dateStr).toLocaleDateString();
      }
    }
  };

  // Routes
  const routes = [
    { path: '/', component: Home },
    { path: '/listings', component: Listings },
    { path: '/post', component: { template: '<div class="container mx-auto px-4 py-8"><h1 class="text-3xl font-bold text-gray-900 mb-6">Post New Listing</h1><p class="text-gray-600">Create a new listing form will be here.</p></div>' } }
  ];

  // Create router
  const router = createRouter({
    history: createWebHistory('/dmsoko/'),
    routes
  });

  // Create pinia store
  const pinia = createPinia();

  // Simple App component
  const App = {
    template: `
      <div id="app">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg">
          <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
              <div class="flex items-center">
                <router-link to="/" class="text-2xl font-bold text-blue-600">DMSoko</router-link>
              </div>
              <div class="hidden md:flex space-x-6">
                <router-link to="/" class="text-gray-700 hover:text-blue-600">Home</router-link>
                <router-link to="/listings" class="text-gray-700 hover:text-blue-600">Browse</router-link>
                <router-link to="/post" class="text-gray-700 hover:text-blue-600">Post Ad</router-link>
              </div>
              <div class="flex items-center space-x-4">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Sign In</button>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main Content -->
        <main>
          <router-view></router-view>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-8 mt-12">
          <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 DMSoko. All rights reserved.</p>
          </div>
        </footer>
      </div>
    `
  };

  // Create and mount app
  const app = createApp(App);
  app.use(router);
  app.use(pinia);

  // Global properties for Frappe integration
  app.config.globalProperties.$frappe = window.frappe;
  app.config.globalProperties.$call = window.frappe.call;

  // Mount app
  app.mount('#dmsoko-app');

  console.log('DMSoko Vue app mounted successfully');
});
