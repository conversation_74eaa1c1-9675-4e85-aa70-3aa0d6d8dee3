import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWishlistStore = defineStore('wishlist', () => {
  // State
  const wishlistItems = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const wishlistCount = computed(() => wishlistItems.value.length)
  
  const isInWishlist = computed(() => {
    return (listingId) => {
      return wishlistItems.value.some(item => item.listing_id === listingId || item.name === listingId)
    }
  })

  // Actions
  const fetchWishlist = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.getWishlist()

      if (response.message && response.message.success) {
        wishlistItems.value = response.message.data || []
      }
    } catch (err) {
      console.error('Failed to fetch wishlist:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const addToWishlist = async (listingId) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.addToWishlist(listingId)

      if (response.message && response.message.success) {
        // Add to local state if not already present
        if (!isInWishlist.value(listingId)) {
          wishlistItems.value.push({
            listing_id: listingId,
            name: listingId,
            creation: new Date().toISOString()
          })
        }
        return { success: true }
      } else {
        throw new Error(response.message?.error || 'Failed to add to wishlist')
      }
    } catch (err) {
      console.error('Failed to add to wishlist:', err)
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const removeFromWishlist = async (listingId) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.removeFromWishlist(listingId)

      if (response.message && response.message.success) {
        // Remove from local state
        wishlistItems.value = wishlistItems.value.filter(
          item => item.listing_id !== listingId && item.name !== listingId
        )
        return { success: true }
      } else {
        throw new Error(response.message?.error || 'Failed to remove from wishlist')
      }
    } catch (err) {
      console.error('Failed to remove from wishlist:', err)
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const toggleWishlist = async (listingId) => {
    if (isInWishlist.value(listingId)) {
      return await removeFromWishlist(listingId)
    } else {
      return await addToWishlist(listingId)
    }
  }

  const clearWishlist = async () => {
    try {
      loading.value = true
      error.value = null

      // Remove all items one by one (if no bulk API available)
      const promises = wishlistItems.value.map(item => 
        window.apiService.removeFromWishlist(item.listing_id || item.name)
      )

      await Promise.all(promises)
      wishlistItems.value = []
      
      return { success: true }
    } catch (err) {
      console.error('Failed to clear wishlist:', err)
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    wishlistItems,
    loading,
    error,
    
    // Getters
    wishlistCount,
    isInWishlist,
    
    // Actions
    fetchWishlist,
    addToWishlist,
    removeFromWishlist,
    toggleWishlist,
    clearWishlist,
    clearError
  }
})
