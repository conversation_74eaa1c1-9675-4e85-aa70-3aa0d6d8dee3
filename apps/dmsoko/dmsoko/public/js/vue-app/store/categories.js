import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useCategoriesStore = defineStore('categories', () => {
  // State
  const categories = ref([])
  const categoryTree = ref([])
  const popularCategories = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const categoriesCount = computed(() => categories.value.length)
  
  const getCategoryById = computed(() => {
    return (categoryId) => {
      return categories.value.find(cat => cat.name === categoryId)
    }
  })

  const getCategoryBySlug = computed(() => {
    return (slug) => {
      return categories.value.find(cat => cat.slug === slug || cat.name === slug)
    }
  })

  const getSubcategories = computed(() => {
    return (parentId) => {
      return categories.value.filter(cat => cat.parent_category === parentId)
    }
  })

  const getRootCategories = computed(() => {
    return categories.value.filter(cat => !cat.parent_category)
  })

  // Actions
  const fetchCategories = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.getCategories()

      if (response.message && response.message.success) {
        categories.value = response.message.data || []
      }
    } catch (err) {
      console.error('Failed to fetch categories:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const fetchCategoryTree = async () => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.getCategoryTree()

      if (response.message && response.message.success) {
        categoryTree.value = response.message.data || []
      }
    } catch (err) {
      console.error('Failed to fetch category tree:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const fetchPopularCategories = async (limit = 10) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.getPopularCategories(limit)

      if (response.message && response.message.success) {
        popularCategories.value = response.message.data || []
      }
    } catch (err) {
      console.error('Failed to fetch popular categories:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    categories,
    categoryTree,
    popularCategories,
    loading,
    error,
    
    // Getters
    categoriesCount,
    getCategoryById,
    getCategoryBySlug,
    getSubcategories,
    getRootCategories,
    
    // Actions
    fetchCategories,
    fetchCategoryTree,
    fetchPopularCategories,
    clearError
  }
})
