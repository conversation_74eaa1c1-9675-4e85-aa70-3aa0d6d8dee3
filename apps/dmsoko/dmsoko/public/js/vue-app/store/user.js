import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // State
  const user = ref(null)
  const userProfile = ref(null)
  const userStats = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const isInitialized = ref(false)

  // Getters
  const isLoggedIn = computed(() => {
    return user.value && window.frappe?.session?.user !== 'Guest'
  })

  const userFullName = computed(() => {
    return user.value?.full_name || user.value?.first_name || 'User'
  })

  const userEmail = computed(() => {
    return user.value?.email || window.frappe?.session?.user || ''
  })

  const userImage = computed(() => {
    return userProfile.value?.user_image || user.value?.user_image || '/assets/frappe/images/default-avatar.png'
  })

  const userRole = computed(() => {
    return user.value?.role_profile_name || 'User'
  })

  const isAdmin = computed(() => {
    return user.value?.roles?.includes('DMSoko Admin') || false
  })

  // Actions
  const initializeSession = async () => {
    if (isInitialized.value) return

    try {
      loading.value = true
      error.value = null

      // Check if user is already logged in via Frappe session
      if (window.frappe?.session?.user && window.frappe.session.user !== 'Guest') {
        await fetchUserProfile()
        await fetchUserStats()
      }

      isInitialized.value = true
    } catch (err) {
      console.error('Failed to initialize session:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const fetchUserProfile = async () => {
    try {
      const response = await window.frappe.call({
        method: 'dmsoko.api.users.get_user_profile',
        args: {}
      })
      
      if (response.message) {
        user.value = response.message
      }
    } catch (err) {
      console.error('Failed to fetch user profile:', err)
      throw err
    }
  }

  const login = async (email, password) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'login',
        args: {
          usr: email,
          pwd: password
        }
      })

      if (response.message === 'Logged In') {
        await fetchUserProfile()
        return { success: true }
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (err) {
      error.value = err.message || 'Login failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const register = async (userData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.users.register_user',
        args: userData
      })

      if (response.message.success) {
        // Auto-login after successful registration
        await login(userData.email, userData.password)
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Registration failed')
      }
    } catch (err) {
      error.value = err.message || 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      await window.frappe.call({
        method: 'logout'
      })
      
      user.value = null
      
      // Redirect to home page
      window.location.href = '/dmsoko'
    } catch (err) {
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.users.update_user_profile',
        args: profileData
      })

      if (response.message.success) {
        await fetchUserProfile()
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Profile update failed')
      }
    } catch (err) {
      error.value = err.message || 'Profile update failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const fetchUserStats = async () => {
    try {
      const response = await window.apiService.getUserStats()

      if (response.message && response.message.success) {
        userStats.value = response.message.data
      }
    } catch (err) {
      console.error('Failed to fetch user stats:', err)
      // Don't throw error for stats as it's not critical
    }
  }

  const resetPassword = async (email) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.apiService.resetPassword(email)

      if (response.message && response.message.success) {
        return { success: true, message: 'Password reset email sent' }
      } else {
        throw new Error(response.message?.error || 'Failed to send reset email')
      }
    } catch (err) {
      console.error('Password reset failed:', err)
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    userProfile,
    userStats,
    loading,
    error,
    isInitialized,

    // Getters
    isLoggedIn,
    userFullName,
    userEmail,
    userImage,
    userRole,
    isAdmin,

    // Actions
    initializeSession,
    fetchUserProfile,
    fetchUserStats,
    login,
    register,
    logout,
    updateProfile,
    resetPassword,
    clearError
  }
})
