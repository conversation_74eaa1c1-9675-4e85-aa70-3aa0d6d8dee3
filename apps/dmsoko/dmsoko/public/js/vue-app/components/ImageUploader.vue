<template>
  <div class="image-uploader">
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Images ({{ images.length }}/{{ maxFiles }})
      </label>
      <p class="text-sm text-gray-500 mb-4">
        Upload up to {{ maxFiles }} images. First image will be used as the main image.
      </p>
    </div>

    <!-- Upload Area -->
    <div
      @drop="handleDrop"
      @dragover.prevent
      @dragenter.prevent
      :class="[
        'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
        isDragging ? 'border-orange-400 bg-orange-50' : 'border-gray-300 hover:border-gray-400'
      ]"
    >
      <input
        ref="fileInput"
        type="file"
        multiple
        accept="image/*"
        @change="handleFileSelect"
        class="hidden"
      />
      
      <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      
      <div class="mt-4">
        <button
          type="button"
          @click="$refs.fileInput.click()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-orange-600 bg-orange-100 hover:bg-orange-200 transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Choose Images
        </button>
        <p class="mt-2 text-sm text-gray-500">or drag and drop images here</p>
      </div>
      
      <p class="text-xs text-gray-500 mt-2">
        PNG, JPG, GIF up to 5MB each
      </p>
    </div>

    <!-- Error Messages -->
    <div v-if="errors.length > 0" class="mt-4">
      <div v-for="error in errors" :key="error" class="text-sm text-red-600 mb-1">
        {{ error }}
      </div>
    </div>

    <!-- Image Preview Grid -->
    <div v-if="images.length > 0" class="mt-6">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="relative group"
        >
          <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
            <img
              :src="getImageUrl(image)"
              :alt="`Image ${index + 1}`"
              class="w-full h-full object-cover"
            />
          </div>
          
          <!-- Main Image Badge -->
          <div v-if="index === 0" class="absolute top-2 left-2">
            <span class="bg-orange-600 text-white px-2 py-1 text-xs font-medium rounded-full">
              Main
            </span>
          </div>
          
          <!-- Actions -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <div class="flex space-x-1">
              <button
                v-if="index !== 0"
                @click="makeMain(index)"
                type="button"
                class="p-1 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full text-gray-600 hover:text-orange-600 transition-colors"
                title="Make main image"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              </button>
              <button
                @click="removeImage(index)"
                type="button"
                class="p-1 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full text-gray-600 hover:text-red-600 transition-colors"
                title="Remove image"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Upload Progress -->
          <div v-if="image.uploading" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
            <div class="text-white text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div class="text-sm">Uploading...</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'ImageUploader',
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    maxFiles: {
      type: Number,
      default: 8
    },
    maxFileSize: {
      type: Number,
      default: 5 * 1024 * 1024 // 5MB
    }
  },
  emits: ['update:modelValue', 'upload', 'remove'],
  setup(props, { emit }) {
    const isDragging = ref(false)
    const errors = ref([])

    const images = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    })

    const validateFile = (file) => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      
      if (!validTypes.includes(file.type)) {
        return 'Invalid file type. Please upload JPG, PNG, GIF, or WebP images.'
      }
      
      if (file.size > props.maxFileSize) {
        return `File size too large. Maximum size is ${props.maxFileSize / (1024 * 1024)}MB.`
      }
      
      return null
    }

    const processFiles = async (files) => {
      errors.value = []
      const fileArray = Array.from(files)
      
      // Check total file count
      if (images.value.length + fileArray.length > props.maxFiles) {
        errors.value.push(`Maximum ${props.maxFiles} images allowed.`)
        return
      }
      
      const validFiles = []
      
      for (const file of fileArray) {
        const error = validateFile(file)
        if (error) {
          errors.value.push(`${file.name}: ${error}`)
        } else {
          validFiles.push(file)
        }
      }
      
      if (validFiles.length === 0) return
      
      // Create preview URLs and add to images array
      const newImages = validFiles.map(file => ({
        file,
        url: URL.createObjectURL(file),
        name: file.name,
        uploading: false,
        uploaded: false
      }))
      
      images.value = [...images.value, ...newImages]
      
      // Upload files
      for (let i = 0; i < newImages.length; i++) {
        const imageIndex = images.value.length - newImages.length + i
        await uploadImage(imageIndex)
      }
    }

    const uploadImage = async (index) => {
      const image = images.value[index]
      if (!image || image.uploaded) return
      
      try {
        image.uploading = true
        
        // Use the API service to upload the file
        const response = await window.apiService.uploadFile(image.file, 'Listing')
        
        if (response.message && response.message.file_url) {
          image.uploaded = true
          image.serverUrl = response.message.file_url
          emit('upload', images.value)
        } else {
          throw new Error('Upload failed')
        }
      } catch (error) {
        console.error('Upload failed:', error)
        errors.value.push(`Failed to upload ${image.name}`)
        removeImage(index)
      } finally {
        image.uploading = false
      }
    }

    const handleFileSelect = (event) => {
      const files = event.target.files
      if (files.length > 0) {
        processFiles(files)
      }
      // Reset input
      event.target.value = ''
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragging.value = false
      
      const files = event.dataTransfer.files
      if (files.length > 0) {
        processFiles(files)
      }
    }

    const removeImage = (index) => {
      const image = images.value[index]
      
      // Revoke object URL to free memory
      if (image.url && image.url.startsWith('blob:')) {
        URL.revokeObjectURL(image.url)
      }
      
      images.value.splice(index, 1)
      emit('remove', index)
      emit('update:modelValue', images.value)
    }

    const makeMain = (index) => {
      const image = images.value.splice(index, 1)[0]
      images.value.unshift(image)
      emit('update:modelValue', images.value)
    }

    const getImageUrl = (image) => {
      return image.serverUrl || image.url
    }

    return {
      isDragging,
      errors,
      images,
      handleFileSelect,
      handleDrop,
      removeImage,
      makeMain,
      getImageUrl
    }
  }
}
</script>

<style scoped>
.aspect-w-1 {
  position: relative;
  padding-bottom: 100%;
}

.aspect-h-1 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
