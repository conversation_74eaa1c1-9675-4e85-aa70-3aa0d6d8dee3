<template>
  <div class="user-listing-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
    <div class="flex flex-col lg:flex-row lg:items-center gap-4">
      <!-- Image -->
      <div class="flex-shrink-0">
        <img
          :src="getImageUrl(listing)"
          :alt="listing.title"
          class="w-24 h-24 lg:w-20 lg:h-20 object-cover rounded-lg"
          @error="handleImageError"
        />
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
          <!-- Main Info -->
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 truncate">{{ listing.title }}</h3>
            <p class="text-gray-600 text-sm mt-1 line-clamp-2">{{ listing.description }}</p>
            
            <div class="flex items-center gap-4 mt-3 text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ listing.location || 'No location' }}
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {{ listing.views_count || 0 }} views
              </div>
              <div>{{ formatDate(listing.creation) }}</div>
            </div>
          </div>

          <!-- Price and Status -->
          <div class="flex flex-col lg:items-end gap-2">
            <div class="text-xl font-bold text-orange-600">
              {{ formatPrice(listing.price) }}
            </div>
            <div>
              <span :class="getStatusBadgeClass(listing.status)" class="px-3 py-1 text-xs font-medium rounded-full">
                {{ getStatusText(listing.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex lg:flex-col gap-2 lg:ml-4">
        <button
          @click="$emit('view', listing)"
          class="flex-1 lg:flex-none px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
        >
          View
        </button>
        <button
          @click="$emit('edit', listing)"
          class="flex-1 lg:flex-none px-3 py-2 text-sm font-medium text-orange-600 bg-orange-50 hover:bg-orange-100 rounded-md transition-colors"
        >
          Edit
        </button>
        <button
          @click="confirmDelete"
          class="flex-1 lg:flex-none px-3 py-2 text-sm font-medium text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-colors"
        >
          Delete
        </button>
      </div>
    </div>

    <!-- Additional Info Row -->
    <div class="mt-4 pt-4 border-t border-gray-100 flex flex-wrap items-center justify-between gap-4 text-sm text-gray-500">
      <div class="flex items-center gap-4">
        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
          {{ listing.category_name || listing.category }}
        </span>
        <span v-if="listing.condition">{{ listing.condition }}</span>
        <span v-if="listing.listing_type">{{ listing.listing_type }}</span>
      </div>
      
      <div class="flex items-center gap-4">
        <span v-if="listing.is_featured" class="text-orange-600 font-medium">Featured</span>
        <span>ID: {{ listing.name }}</span>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="showDeleteModal = false">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Listing</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              Are you sure you want to delete "{{ listing.title }}"? This action cannot be undone.
            </p>
          </div>
          <div class="flex justify-center gap-3 mt-4">
            <button
              @click="showDeleteModal = false"
              class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md hover:bg-gray-400 transition-colors"
            >
              Cancel
            </button>
            <button
              @click="handleDelete"
              class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'UserListingCard',
  props: {
    listing: {
      type: Object,
      required: true
    }
  },
  emits: ['view', 'edit', 'delete'],
  setup(props, { emit }) {
    const showDeleteModal = ref(false)
    const imageError = ref(false)

    const getImageUrl = (listing) => {
      if (imageError.value) return '/assets/dmsoko/images/placeholder.jpg'
      
      if (listing.image_url) {
        return listing.image_url.startsWith('http') 
          ? listing.image_url 
          : `/files/${listing.image_url}`
      }
      
      if (listing.images && listing.images.length > 0) {
        const firstImage = listing.images[0]
        return firstImage.startsWith('http') 
          ? firstImage 
          : `/files/${firstImage}`
      }
      
      return '/assets/dmsoko/images/placeholder.jpg'
    }

    const handleImageError = () => {
      imageError.value = true
    }

    const getStatusBadgeClass = (status) => {
      const classes = {
        'Draft': 'bg-gray-100 text-gray-800',
        'Pending': 'bg-yellow-100 text-yellow-800',
        'Published': 'bg-green-100 text-green-800',
        'Sold': 'bg-blue-100 text-blue-800',
        'Expired': 'bg-red-100 text-red-800',
        'Rejected': 'bg-red-100 text-red-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }

    const getStatusText = (status) => {
      const texts = {
        'Draft': 'Draft',
        'Pending': 'Pending',
        'Published': 'Live',
        'Sold': 'Sold',
        'Expired': 'Expired',
        'Rejected': 'Rejected'
      }
      return texts[status] || status
    }

    const formatPrice = (price) => {
      if (!price || price === 0) return 'Price on request'
      
      return new Intl.NumberFormat('en-TZ', {
        style: 'currency',
        currency: 'TZS',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price)
    }

    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      
      const date = new Date(dateStr)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const confirmDelete = () => {
      showDeleteModal.value = true
    }

    const handleDelete = () => {
      showDeleteModal.value = false
      emit('delete', props.listing)
    }

    return {
      showDeleteModal,
      imageError,
      getImageUrl,
      handleImageError,
      getStatusBadgeClass,
      getStatusText,
      formatPrice,
      formatDate,
      confirmDelete,
      handleDelete
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
