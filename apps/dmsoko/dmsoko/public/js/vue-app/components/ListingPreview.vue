<template>
  <div class="listing-preview bg-gray-50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
    
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <!-- Image Section -->
      <div class="relative">
        <div v-if="listing.images && listing.images.length > 0" class="aspect-w-16 aspect-h-9 bg-gray-200">
          <img
            :src="getMainImageUrl()"
            :alt="listing.title"
            class="w-full h-64 object-cover"
          />
          <div v-if="listing.images.length > 1" class="absolute bottom-4 right-4 bg-black bg-opacity-60 text-white px-2 py-1 rounded text-sm">
            +{{ listing.images.length - 1 }} more
          </div>
        </div>
        <div v-else class="h-64 bg-gray-200 flex items-center justify-center">
          <div class="text-center text-gray-500">
            <svg class="mx-auto h-12 w-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <p>No images uploaded</p>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="p-6">
        <!-- Title and Price -->
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-4">
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">
              {{ listing.title || 'Listing Title' }}
            </h1>
            <div class="flex items-center gap-4 text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ listing.location || 'Location not specified' }}
              </div>
              <div>{{ formatDate(new Date()) }}</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-3xl font-bold text-orange-600 mb-1">
              {{ formatPrice(listing.price) }}
            </div>
            <div v-if="listing.listing_type" class="text-sm text-gray-500">
              {{ listing.listing_type }}
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
          <div class="text-gray-700 whitespace-pre-wrap">
            {{ listing.description || 'No description provided.' }}
          </div>
        </div>

        <!-- Details Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Category -->
          <div v-if="listing.category">
            <h4 class="text-sm font-medium text-gray-500 mb-1">Category</h4>
            <p class="text-gray-900">{{ getCategoryName(listing.category) }}</p>
          </div>

          <!-- Condition -->
          <div v-if="listing.condition">
            <h4 class="text-sm font-medium text-gray-500 mb-1">Condition</h4>
            <p class="text-gray-900">{{ listing.condition }}</p>
          </div>

          <!-- Listing Type -->
          <div v-if="listing.listing_type">
            <h4 class="text-sm font-medium text-gray-500 mb-1">Type</h4>
            <p class="text-gray-900">{{ listing.listing_type }}</p>
          </div>

          <!-- Price -->
          <div>
            <h4 class="text-sm font-medium text-gray-500 mb-1">Price</h4>
            <p class="text-gray-900">{{ formatPrice(listing.price) }}</p>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-if="listing.contact_phone">
              <h4 class="text-sm font-medium text-gray-500 mb-1">Phone</h4>
              <p class="text-gray-900">{{ listing.contact_phone }}</p>
            </div>
            <div v-if="listing.contact_email">
              <h4 class="text-sm font-medium text-gray-500 mb-1">Email</h4>
              <p class="text-gray-900">{{ listing.contact_email }}</p>
            </div>
          </div>
        </div>

        <!-- Action Buttons (Preview) -->
        <div class="border-t border-gray-200 pt-6 mt-6">
          <div class="flex flex-col sm:flex-row gap-3">
            <button
              type="button"
              disabled
              class="flex-1 bg-orange-600 text-white py-3 px-6 rounded-md font-medium opacity-50 cursor-not-allowed"
            >
              Contact Seller
            </button>
            <button
              type="button"
              disabled
              class="flex-1 bg-white text-gray-700 border border-gray-300 py-3 px-6 rounded-md font-medium opacity-50 cursor-not-allowed"
            >
              Add to Wishlist
            </button>
            <button
              type="button"
              disabled
              class="bg-white text-gray-700 border border-gray-300 py-3 px-6 rounded-md font-medium opacity-50 cursor-not-allowed"
            >
              Share
            </button>
          </div>
          <p class="text-xs text-gray-500 mt-2 text-center">
            This is a preview. Buttons will be functional after publishing.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useCategoriesStore } from '../store/categories.js'

export default {
  name: 'ListingPreview',
  props: {
    listing: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const categoriesStore = useCategoriesStore()

    const getMainImageUrl = () => {
      if (!props.listing.images || props.listing.images.length === 0) return null
      
      const mainImage = props.listing.images[0]
      return mainImage.serverUrl || mainImage.url
    }

    const getCategoryName = (categoryId) => {
      const category = categoriesStore.categories.find(cat => cat.name === categoryId)
      return category ? category.category_name : categoryId
    }

    const formatPrice = (price) => {
      if (!price || price === 0) return 'Price on request'
      
      return new Intl.NumberFormat('en-TZ', {
        style: 'currency',
        currency: 'TZS',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price)
    }

    const formatDate = (date) => {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    return {
      getMainImageUrl,
      getCategoryName,
      formatPrice,
      formatDate
    }
  }
}
</script>

<style scoped>
.aspect-w-16 {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.aspect-h-9 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
