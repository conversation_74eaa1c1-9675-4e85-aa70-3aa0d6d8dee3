<template>
  <div 
    class="listing-card group cursor-pointer"
    :class="{ 'compact': compact }"
    @click="viewListing"
  >
    <!-- Image -->
    <div class="relative overflow-hidden">
      <img
        :src="listing.primary_image || '/assets/dmsoko/images/placeholder.jpg'"
        :alt="listing.title"
        class="listing-card-image group-hover:scale-105 transition-transform duration-300"
      />
      
      <!-- Featured Badge -->
      <div v-if="listing.featured" class="absolute top-2 left-2">
        <span class="bg-orange-600 text-white px-2 py-1 text-xs font-medium rounded-full flex items-center">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          Featured
        </span>
      </div>

      <!-- Wishlist Button -->
      <button
        v-if="!isOwner"
        @click.stop="toggleWishlist"
        class="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full flex items-center justify-center transition-all shadow-sm"
        :class="{ 'text-red-500': inWishlist, 'text-gray-400 hover:text-red-500': !inWishlist }"
      >
        <svg class="w-4 h-4" :fill="inWishlist ? 'currentColor' : 'none'" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
      </button>
    </div>

    <!-- Content -->
    <div class="listing-card-content">
      <!-- Title -->
      <h3 class="listing-card-title">{{ listing.title }}</h3>
      
      <!-- Price -->
      <div class="listing-card-price">
        <span v-if="listing.price">
          {{ formatPrice(listing.price, listing.currency) }}
        </span>
        <span v-else class="text-gray-500">Price on request</span>
      </div>
      
      <!-- Location and Date -->
      <div class="flex items-center justify-between text-sm text-gray-500 mt-2">
        <div class="listing-card-location flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          {{ listing.location }}
        </div>
        <div>
          {{ formatDate(listing.creation) }}
        </div>
      </div>

      <!-- Category and Type (for non-compact) -->
      <div v-if="!compact" class="flex items-center justify-between mt-3">
        <span class="bg-orange-100 text-orange-800 px-2 py-1 text-xs font-medium rounded-full">{{ listing.category_name }}</span>
        <span class="text-xs text-gray-500">{{ listing.listing_type }}</span>
      </div>

      <!-- Views (for non-compact) -->
      <div v-if="!compact" class="flex items-center justify-between mt-2 text-xs text-gray-500">
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          {{ listing.views_count }} views
        </span>
        <span v-if="listing.user_name">by {{ listing.user_name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'

const props = defineProps({
  listing: {
    type: Object,
    required: true
  },
  compact: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const userStore = useUserStore()

// Data
const inWishlist = ref(false)

// Computed
const isOwner = computed(() => {
  return userStore.user && userStore.user.name === props.listing.created_by_user
})

// Methods
const viewListing = () => {
  router.push(`/listing/${props.listing.name}`)
}

const formatPrice = (price, currency) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
  return formatter.format(price)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

const toggleWishlist = async () => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  try {
    const method = inWishlist.value 
      ? 'dmsoko.api.wishlist.remove_from_wishlist'
      : 'dmsoko.api.wishlist.add_to_wishlist'
    
    const response = await window.frappe.call({
      method: method,
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = !inWishlist.value
    }
  } catch (error) {
    console.error('Failed to toggle wishlist:', error)
  }
}

const checkWishlistStatus = async () => {
  if (!userStore.isLoggedIn) return
  
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.wishlist.check_wishlist_status',
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = response.message.data.in_wishlist
    }
  } catch (error) {
    console.error('Failed to check wishlist status:', error)
  }
}

// Lifecycle
onMounted(() => {
  checkWishlistStatus()
})
</script>

<style scoped>
.listing-card.compact .listing-card-image {
  height: 150px;
}

.listing-card:not(.compact) .listing-card-image {
  height: 200px;
}
</style>
