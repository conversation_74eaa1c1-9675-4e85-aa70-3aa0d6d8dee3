<template>
  <div class="post-listing-page bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">
              {{ isEditing ? 'Edit Listing' : 'Post New Listing' }}
            </h1>
            <p class="text-gray-600 mt-1">
              {{ isEditing ? 'Update your listing details' : 'Create a new listing to sell your item' }}
            </p>
          </div>
          <router-link
            to="/dashboard"
            class="text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </router-link>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Progress Steps -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <div
              v-for="(step, index) in steps"
              :key="step.key"
              class="flex items-center"
              :class="{ 'flex-1': index < steps.length - 1 }"
            >
              <div class="flex items-center">
                <div
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                    currentStep >= index + 1
                      ? 'bg-orange-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  ]"
                >
                  {{ index + 1 }}
                </div>
                <span
                  :class="[
                    'ml-2 text-sm font-medium',
                    currentStep >= index + 1 ? 'text-orange-600' : 'text-gray-500'
                  ]"
                >
                  {{ step.title }}
                </span>
              </div>
              <div
                v-if="index < steps.length - 1"
                :class="[
                  'flex-1 h-0.5 mx-4',
                  currentStep > index + 1 ? 'bg-orange-600' : 'bg-gray-200'
                ]"
              ></div>
            </div>
          </div>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="bg-white rounded-lg shadow-sm">
          <!-- Step 1: Basic Information -->
          <div v-if="currentStep === 1" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Title -->
              <div class="md:col-span-2">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                  Title *
                </label>
                <input
                  id="title"
                  v-model="form.title"
                  type="text"
                  required
                  placeholder="Enter a descriptive title for your listing"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.title }"
                />
                <p v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</p>
              </div>

              <!-- Category -->
              <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                  Category *
                </label>
                <select
                  id="category"
                  v-model="form.category"
                  required
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.category }"
                >
                  <option value="">Select a category</option>
                  <option v-for="category in categories" :key="category.name" :value="category.name">
                    {{ category.category_name }}
                  </option>
                </select>
                <p v-if="errors.category" class="mt-1 text-sm text-red-600">{{ errors.category }}</p>
              </div>

              <!-- Listing Type -->
              <div>
                <label for="listing_type" class="block text-sm font-medium text-gray-700 mb-2">
                  Listing Type *
                </label>
                <select
                  id="listing_type"
                  v-model="form.listing_type"
                  required
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.listing_type }"
                >
                  <option value="">Select type</option>
                  <option value="For Sale">For Sale</option>
                  <option value="For Rent">For Rent</option>
                  <option value="Wanted">Wanted</option>
                  <option value="Services">Services</option>
                </select>
                <p v-if="errors.listing_type" class="mt-1 text-sm text-red-600">{{ errors.listing_type }}</p>
              </div>

              <!-- Price -->
              <div>
                <label for="price" class="block text-sm font-medium text-gray-700 mb-2">
                  Price (TZS)
                </label>
                <input
                  id="price"
                  v-model.number="form.price"
                  type="number"
                  min="0"
                  step="1000"
                  placeholder="Enter price or leave empty for 'Price on request'"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.price }"
                />
                <p v-if="errors.price" class="mt-1 text-sm text-red-600">{{ errors.price }}</p>
              </div>

              <!-- Condition -->
              <div>
                <label for="condition" class="block text-sm font-medium text-gray-700 mb-2">
                  Condition
                </label>
                <select
                  id="condition"
                  v-model="form.condition"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="">Select condition</option>
                  <option value="New">New</option>
                  <option value="Like New">Like New</option>
                  <option value="Good">Good</option>
                  <option value="Fair">Fair</option>
                  <option value="Poor">Poor</option>
                </select>
              </div>

              <!-- Description -->
              <div class="md:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <textarea
                  id="description"
                  v-model="form.description"
                  required
                  rows="4"
                  placeholder="Describe your item in detail. Include features, condition, and any other relevant information."
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.description }"
                ></textarea>
                <p v-if="errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</p>
                <p class="mt-1 text-sm text-gray-500">{{ form.description.length }}/1000 characters</p>
              </div>
            </div>
          </div>

          <!-- Step 2: Images -->
          <div v-if="currentStep === 2" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Add Images</h2>
            
            <ImageUploader
              v-model="form.images"
              :max-files="8"
              @upload="handleImageUpload"
              @remove="handleImageRemove"
            />
          </div>

          <!-- Step 3: Location & Contact -->
          <div v-if="currentStep === 3" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Location & Contact</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Location -->
              <div class="md:col-span-2">
                <label for="location" class="block text-sm font-medium text-gray-700 mb-2">
                  Location *
                </label>
                <input
                  id="location"
                  v-model="form.location"
                  type="text"
                  required
                  placeholder="Enter city, area, or specific location"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  :class="{ 'border-red-300': errors.location }"
                />
                <p v-if="errors.location" class="mt-1 text-sm text-red-600">{{ errors.location }}</p>
              </div>

              <!-- Contact Phone -->
              <div>
                <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <input
                  id="contact_phone"
                  v-model="form.contact_phone"
                  type="tel"
                  placeholder="Enter phone number"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>

              <!-- Contact Email -->
              <div>
                <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email
                </label>
                <input
                  id="contact_email"
                  v-model="form.contact_email"
                  type="email"
                  placeholder="Enter email address"
                  class="w-full px-3 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>
          </div>

          <!-- Step 4: Review & Publish -->
          <div v-if="currentStep === 4" class="p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Review & Publish</h2>
            
            <ListingPreview :listing="form" />
            
            <div class="mt-6 p-4 bg-orange-50 rounded-lg">
              <div class="flex items-start">
                <svg class="w-5 h-5 text-orange-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h3 class="text-sm font-medium text-orange-800">Before you publish</h3>
                  <p class="text-sm text-orange-700 mt-1">
                    Please review your listing carefully. Once published, it will be visible to all users and may require admin approval.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-between">
            <button
              v-if="currentStep > 1"
              type="button"
              @click="previousStep"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Previous
            </button>
            <div v-else></div>

            <div class="flex gap-3">
              <button
                v-if="currentStep < 4"
                type="button"
                @click="saveDraft"
                :disabled="saving"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
              >
                {{ saving ? 'Saving...' : 'Save Draft' }}
              </button>
              
              <button
                v-if="currentStep < 4"
                type="button"
                @click="nextStep"
                class="px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 transition-colors"
              >
                Next
              </button>
              
              <button
                v-else
                type="submit"
                :disabled="submitting"
                class="px-6 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700 disabled:opacity-50 transition-colors"
              >
                {{ submitting ? 'Publishing...' : (isEditing ? 'Update Listing' : 'Publish Listing') }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'
import { useCategoriesStore } from '../store/categories.js'
import ImageUploader from '../components/ImageUploader.vue'
import ListingPreview from '../components/ListingPreview.vue'

export default {
  name: 'PostListing',
  components: {
    ImageUploader,
    ListingPreview
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const userStore = useUserStore()
    const categoriesStore = useCategoriesStore()

    const currentStep = ref(1)
    const saving = ref(false)
    const submitting = ref(false)
    const errors = ref({})

    const isEditing = computed(() => !!route.params.id)

    const steps = [
      { key: 'basic', title: 'Basic Info' },
      { key: 'images', title: 'Images' },
      { key: 'location', title: 'Location' },
      { key: 'review', title: 'Review' }
    ]

    const form = ref({
      title: '',
      category: '',
      listing_type: '',
      price: null,
      condition: '',
      description: '',
      location: '',
      contact_phone: '',
      contact_email: '',
      images: []
    })

    const categories = computed(() => categoriesStore.categories)

    const validateStep = (step) => {
      errors.value = {}
      
      if (step === 1) {
        if (!form.value.title) errors.value.title = 'Title is required'
        if (!form.value.category) errors.value.category = 'Category is required'
        if (!form.value.listing_type) errors.value.listing_type = 'Listing type is required'
        if (!form.value.description) errors.value.description = 'Description is required'
        if (form.value.description.length > 1000) errors.value.description = 'Description must be less than 1000 characters'
      }
      
      if (step === 3) {
        if (!form.value.location) errors.value.location = 'Location is required'
      }
      
      return Object.keys(errors.value).length === 0
    }

    const nextStep = () => {
      if (validateStep(currentStep.value)) {
        currentStep.value++
      }
    }

    const previousStep = () => {
      currentStep.value--
    }

    const saveDraft = async () => {
      saving.value = true
      try {
        const listingData = { ...form.value, status: 'Draft' }
        
        if (isEditing.value) {
          await window.apiService.updateListing(route.params.id, listingData)
        } else {
          await window.apiService.createListing(listingData)
        }
        
        // Show success message
        alert('Draft saved successfully!')
      } catch (error) {
        console.error('Failed to save draft:', error)
        alert('Failed to save draft. Please try again.')
      } finally {
        saving.value = false
      }
    }

    const handleSubmit = async () => {
      if (!validateStep(4)) return
      
      submitting.value = true
      try {
        const listingData = { ...form.value, status: 'Pending' }
        
        if (isEditing.value) {
          await window.apiService.updateListing(route.params.id, listingData)
        } else {
          await window.apiService.createListing(listingData)
        }
        
        router.push('/dashboard?tab=pending')
      } catch (error) {
        console.error('Failed to submit listing:', error)
        alert('Failed to submit listing. Please try again.')
      } finally {
        submitting.value = false
      }
    }

    const handleImageUpload = (images) => {
      form.value.images = images
    }

    const handleImageRemove = (index) => {
      form.value.images.splice(index, 1)
    }

    const loadListing = async () => {
      if (!isEditing.value) return
      
      try {
        const response = await window.apiService.getListing(route.params.id)
        if (response.message && response.message.success) {
          const listing = response.message.data
          Object.assign(form.value, listing)
        }
      } catch (error) {
        console.error('Failed to load listing:', error)
        router.push('/dashboard')
      }
    }

    onMounted(async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login')
        return
      }
      
      await Promise.all([
        categoriesStore.fetchCategories(),
        loadListing()
      ])
    })

    return {
      currentStep,
      saving,
      submitting,
      errors,
      isEditing,
      steps,
      form,
      categories,
      nextStep,
      previousStep,
      saveDraft,
      handleSubmit,
      handleImageUpload,
      handleImageRemove
    }
  }
}
</script>
