<template>
  <div class="dashboard-page bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4 py-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">My Dashboard</h1>
            <p class="text-gray-600 mt-1">Manage your listings and account</p>
          </div>
          <div class="mt-4 md:mt-0">
            <router-link
              to="/post"
              class="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors font-medium"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Post New Listing
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 py-8">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Listings</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalListings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Active Listings</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.activeListings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Views</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalViews }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Messages</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.unreadMessages }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === tab.key
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.label }}
              <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {{ tab.count }}
              </span>
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
          <!-- All Listings Tab -->
          <div v-if="activeTab === 'all'" class="space-y-4">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div class="flex items-center space-x-4">
                <select
                  v-model="listingFilter"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  @change="filterListings"
                >
                  <option value="">All Status</option>
                  <option value="Draft">Draft</option>
                  <option value="Pending">Pending Approval</option>
                  <option value="Published">Published</option>
                  <option value="Sold">Sold</option>
                  <option value="Expired">Expired</option>
                </select>
              </div>
              <div class="text-sm text-gray-500">
                {{ filteredListings.length }} of {{ userListings.length }} listings
              </div>
            </div>

            <!-- Listings List -->
            <div v-if="loading" class="text-center py-8">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
              <p class="mt-4 text-gray-600">Loading your listings...</p>
            </div>

            <div v-else-if="filteredListings.length === 0" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900">No listings found</h3>
              <p class="mt-2 text-gray-500">
                {{ listingFilter ? 'No listings match the selected filter.' : 'You haven\'t created any listings yet.' }}
              </p>
              <div class="mt-6">
                <router-link
                  to="/post"
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors"
                >
                  Create Your First Listing
                </router-link>
              </div>
            </div>

            <div v-else class="space-y-4">
              <UserListingCard
                v-for="listing in filteredListings"
                :key="listing.name"
                :listing="listing"
                @edit="editListing"
                @delete="deleteListing"
                @view="viewListing"
              />
            </div>
          </div>

          <!-- Draft Listings Tab -->
          <div v-else-if="activeTab === 'draft'">
            <DraftListings :listings="draftListings" @edit="editListing" @delete="deleteListing" />
          </div>

          <!-- Pending Listings Tab -->
          <div v-else-if="activeTab === 'pending'">
            <PendingListings :listings="pendingListings" />
          </div>

          <!-- Messages Tab -->
          <div v-else-if="activeTab === 'messages'">
            <MessagesTab />
          </div>

          <!-- Profile Tab -->
          <div v-else-if="activeTab === 'profile'">
            <ProfileSettings />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'
import UserListingCard from '../components/UserListingCard.vue'
import DraftListings from '../components/DraftListings.vue'
import PendingListings from '../components/PendingListings.vue'
import MessagesTab from '../components/MessagesTab.vue'
import ProfileSettings from '../components/ProfileSettings.vue'

export default {
  name: 'Dashboard',
  components: {
    UserListingCard,
    DraftListings,
    PendingListings,
    MessagesTab,
    ProfileSettings
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()

    const activeTab = ref('all')
    const loading = ref(true)
    const userListings = ref([])
    const listingFilter = ref('')
    const stats = ref({
      totalListings: 0,
      activeListings: 0,
      totalViews: 0,
      unreadMessages: 0
    })

    const tabs = computed(() => [
      { key: 'all', label: 'All Listings', count: userListings.value.length },
      { key: 'draft', label: 'Drafts', count: draftListings.value.length },
      { key: 'pending', label: 'Pending', count: pendingListings.value.length },
      { key: 'messages', label: 'Messages', count: stats.value.unreadMessages },
      { key: 'profile', label: 'Profile' }
    ])

    const filteredListings = computed(() => {
      if (!listingFilter.value) return userListings.value
      return userListings.value.filter(listing => listing.status === listingFilter.value)
    })

    const draftListings = computed(() => {
      return userListings.value.filter(listing => listing.status === 'Draft')
    })

    const pendingListings = computed(() => {
      return userListings.value.filter(listing => listing.status === 'Pending')
    })

    const loadUserListings = async () => {
      try {
        loading.value = true
        const response = await window.apiService.getUserListings()
        
        if (response.message && response.message.success) {
          userListings.value = response.message.data.listings || []
          stats.value = {
            totalListings: response.message.data.stats?.total_listings || userListings.value.length,
            activeListings: response.message.data.stats?.active_listings || userListings.value.filter(l => l.status === 'Published').length,
            totalViews: response.message.data.stats?.total_views || 0,
            unreadMessages: response.message.data.stats?.unread_messages || 0
          }
        }
      } catch (error) {
        console.error('Failed to load user listings:', error)
      } finally {
        loading.value = false
      }
    }

    const filterListings = () => {
      // Filtering is handled by computed property
    }

    const editListing = (listing) => {
      router.push(`/edit-listing/${listing.name}`)
    }

    const deleteListing = async (listing) => {
      if (!confirm('Are you sure you want to delete this listing?')) return

      try {
        const response = await window.apiService.deleteListing(listing.name)
        
        if (response.message && response.message.success) {
          await loadUserListings() // Refresh the list
        }
      } catch (error) {
        console.error('Failed to delete listing:', error)
        alert('Failed to delete listing. Please try again.')
      }
    }

    const viewListing = (listing) => {
      router.push(`/listing/${listing.name}`)
    }

    onMounted(async () => {
      if (!userStore.isLoggedIn) {
        router.push('/login')
        return
      }
      
      await loadUserListings()
    })

    return {
      activeTab,
      loading,
      userListings,
      listingFilter,
      stats,
      tabs,
      filteredListings,
      draftListings,
      pendingListings,
      loadUserListings,
      filterListings,
      editListing,
      deleteListing,
      viewListing
    }
  }
}
</script>
