<template>
  <div class="admin-dashboard bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="container mx-auto px-4 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p class="text-gray-600 mt-1">Manage listings, users, and platform settings</p>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
              <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84L3.93 8.96M2 12h4M5.84 16.95l3.12-3.12M12 22v-4M16.95 18.16l3.12 3.12M22 12h-4M18.16 7.05l-3.12-3.12"></path>
                </svg>
              </button>
              <span v-if="stats.pendingApprovals > 0" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                {{ stats.pendingApprovals }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container mx-auto px-4 py-8">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-orange-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Listings</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalListings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Pending Approval</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.pendingApprovals }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Users</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.totalUsers }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">This Month</p>
              <p class="text-2xl font-semibold text-gray-900">{{ stats.thisMonthListings }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === tab.key
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ tab.label }}
              <span v-if="tab.count !== undefined" class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {{ tab.count }}
              </span>
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
          <!-- Pending Approvals Tab -->
          <div v-if="activeTab === 'pending'">
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-lg font-semibold text-gray-900">Pending Approvals</h2>
              <div class="flex items-center space-x-4">
                <button
                  v-if="selectedListings.length > 0"
                  @click="bulkApprove"
                  :disabled="bulkProcessing"
                  class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                  Approve Selected ({{ selectedListings.length }})
                </button>
                <button
                  v-if="selectedListings.length > 0"
                  @click="bulkReject"
                  :disabled="bulkProcessing"
                  class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 transition-colors"
                >
                  Reject Selected ({{ selectedListings.length }})
                </button>
              </div>
            </div>

            <!-- Pending Listings Table -->
            <div v-if="loading" class="text-center py-8">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
              <p class="mt-4 text-gray-600">Loading pending listings...</p>
            </div>

            <div v-else-if="pendingListings.length === 0" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900">All caught up!</h3>
              <p class="mt-2 text-gray-500">No listings pending approval at the moment.</p>
            </div>

            <div v-else class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        :checked="allSelected"
                        @change="toggleSelectAll"
                        class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      />
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Listing
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="listing in pendingListings" :key="listing.name" class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                      <input
                        type="checkbox"
                        :value="listing.name"
                        v-model="selectedListings"
                        class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <div class="flex items-center">
                        <img
                          :src="getListingImage(listing)"
                          :alt="listing.title"
                          class="h-12 w-12 rounded-lg object-cover mr-4"
                        />
                        <div>
                          <div class="text-sm font-medium text-gray-900">{{ listing.title }}</div>
                          <div class="text-sm text-gray-500">{{ listing.location }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900">
                      {{ listing.user_name || listing.owner }}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900">
                      {{ listing.category_name || listing.category }}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900">
                      {{ formatPrice(listing.price) }}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                      {{ formatDate(listing.creation) }}
                    </td>
                    <td class="px-6 py-4 text-sm font-medium space-x-2">
                      <button
                        @click="viewListing(listing)"
                        class="text-orange-600 hover:text-orange-900 transition-colors"
                      >
                        View
                      </button>
                      <button
                        @click="approveListing(listing)"
                        class="text-green-600 hover:text-green-900 transition-colors"
                      >
                        Approve
                      </button>
                      <button
                        @click="rejectListing(listing)"
                        class="text-red-600 hover:text-red-900 transition-colors"
                      >
                        Reject
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- All Listings Tab -->
          <div v-else-if="activeTab === 'all'">
            <AllListingsAdmin />
          </div>

          <!-- Users Tab -->
          <div v-else-if="activeTab === 'users'">
            <UsersAdmin />
          </div>

          <!-- Reports Tab -->
          <div v-else-if="activeTab === 'reports'">
            <ReportsAdmin />
          </div>

          <!-- Settings Tab -->
          <div v-else-if="activeTab === 'settings'">
            <SettingsAdmin />
          </div>
        </div>
      </div>
    </div>

    <!-- Rejection Modal -->
    <div v-if="showRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="showRejectModal = false">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Listing</h3>
          <form @submit.prevent="confirmReject">
            <div class="mb-4">
              <label for="reject-reason" class="block text-sm font-medium text-gray-700 mb-1">
                Reason for rejection
              </label>
              <textarea
                id="reject-reason"
                v-model="rejectReason"
                required
                rows="3"
                placeholder="Please provide a reason for rejecting this listing..."
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              ></textarea>
            </div>
            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="showRejectModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="!rejectReason.trim()"
                class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md disabled:opacity-50 transition-colors"
              >
                Reject Listing
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'

export default {
  name: 'AdminDashboard',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()

    const activeTab = ref('pending')
    const loading = ref(true)
    const bulkProcessing = ref(false)
    const pendingListings = ref([])
    const selectedListings = ref([])
    const showRejectModal = ref(false)
    const rejectReason = ref('')
    const currentRejectListing = ref(null)

    const stats = ref({
      totalListings: 0,
      pendingApprovals: 0,
      totalUsers: 0,
      thisMonthListings: 0
    })

    const tabs = computed(() => [
      { key: 'pending', label: 'Pending Approvals', count: stats.value.pendingApprovals },
      { key: 'all', label: 'All Listings' },
      { key: 'users', label: 'Users' },
      { key: 'reports', label: 'Reports' },
      { key: 'settings', label: 'Settings' }
    ])

    const allSelected = computed(() => {
      return pendingListings.value.length > 0 && selectedListings.value.length === pendingListings.value.length
    })

    const loadPendingListings = async () => {
      try {
        loading.value = true
        const response = await window.apiService.getListings({ status: 'Pending' }, 1, 100)
        
        if (response.message && response.message.success) {
          pendingListings.value = response.message.data.listings || []
        }
      } catch (error) {
        console.error('Failed to load pending listings:', error)
      } finally {
        loading.value = false
      }
    }

    const loadStats = async () => {
      try {
        // Load admin stats - this would be a separate API endpoint
        const response = await window.frappe.call({
          method: 'dmsoko.api.admin.get_dashboard_stats'
        })
        
        if (response.message && response.message.success) {
          stats.value = response.message.data
        }
      } catch (error) {
        console.error('Failed to load admin stats:', error)
      }
    }

    const toggleSelectAll = () => {
      if (allSelected.value) {
        selectedListings.value = []
      } else {
        selectedListings.value = pendingListings.value.map(listing => listing.name)
      }
    }

    const approveListing = async (listing) => {
      try {
        const response = await window.frappe.call({
          method: 'dmsoko.api.admin.approve_listing',
          args: { listing_id: listing.name }
        })
        
        if (response.message && response.message.success) {
          await loadPendingListings()
          await loadStats()
        }
      } catch (error) {
        console.error('Failed to approve listing:', error)
        alert('Failed to approve listing. Please try again.')
      }
    }

    const rejectListing = (listing) => {
      currentRejectListing.value = listing
      showRejectModal.value = true
    }

    const confirmReject = async () => {
      try {
        const response = await window.frappe.call({
          method: 'dmsoko.api.admin.reject_listing',
          args: { 
            listing_id: currentRejectListing.value.name,
            reason: rejectReason.value
          }
        })
        
        if (response.message && response.message.success) {
          showRejectModal.value = false
          rejectReason.value = ''
          currentRejectListing.value = null
          await loadPendingListings()
          await loadStats()
        }
      } catch (error) {
        console.error('Failed to reject listing:', error)
        alert('Failed to reject listing. Please try again.')
      }
    }

    const bulkApprove = async () => {
      if (!confirm(`Are you sure you want to approve ${selectedListings.value.length} listings?`)) return
      
      try {
        bulkProcessing.value = true
        const promises = selectedListings.value.map(listingId =>
          window.frappe.call({
            method: 'dmsoko.api.admin.approve_listing',
            args: { listing_id: listingId }
          })
        )
        
        await Promise.all(promises)
        selectedListings.value = []
        await loadPendingListings()
        await loadStats()
      } catch (error) {
        console.error('Failed to bulk approve:', error)
        alert('Failed to approve some listings. Please try again.')
      } finally {
        bulkProcessing.value = false
      }
    }

    const bulkReject = async () => {
      if (!confirm(`Are you sure you want to reject ${selectedListings.value.length} listings?`)) return
      
      const reason = prompt('Please provide a reason for rejection:')
      if (!reason) return
      
      try {
        bulkProcessing.value = true
        const promises = selectedListings.value.map(listingId =>
          window.frappe.call({
            method: 'dmsoko.api.admin.reject_listing',
            args: { listing_id: listingId, reason }
          })
        )
        
        await Promise.all(promises)
        selectedListings.value = []
        await loadPendingListings()
        await loadStats()
      } catch (error) {
        console.error('Failed to bulk reject:', error)
        alert('Failed to reject some listings. Please try again.')
      } finally {
        bulkProcessing.value = false
      }
    }

    const viewListing = (listing) => {
      router.push(`/listing/${listing.name}`)
    }

    const getListingImage = (listing) => {
      if (listing.image_url) {
        return listing.image_url.startsWith('http') 
          ? listing.image_url 
          : `/files/${listing.image_url}`
      }
      return '/assets/dmsoko/images/placeholder.jpg'
    }

    const formatPrice = (price) => {
      if (!price || price === 0) return 'Price on request'
      
      return new Intl.NumberFormat('en-TZ', {
        style: 'currency',
        currency: 'TZS',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(price)
    }

    const formatDate = (dateStr) => {
      if (!dateStr) return ''
      
      const date = new Date(dateStr)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    onMounted(async () => {
      if (!userStore.isLoggedIn || !userStore.isAdmin) {
        router.push('/login')
        return
      }
      
      await Promise.all([
        loadPendingListings(),
        loadStats()
      ])
    })

    return {
      activeTab,
      loading,
      bulkProcessing,
      pendingListings,
      selectedListings,
      showRejectModal,
      rejectReason,
      currentRejectListing,
      stats,
      tabs,
      allSelected,
      toggleSelectAll,
      approveListing,
      rejectListing,
      confirmReject,
      bulkApprove,
      bulkReject,
      viewListing,
      getListingImage,
      formatPrice,
      formatDate
    }
  }
}
</script>
