# DMSoko Vue.js Frontend

A professional, responsive Vue.js frontend for the DMSoko classifieds platform built on Frappe Framework.

## 🎨 Design System

### Color Scheme
- **Primary**: `#FF6600` (Orange) - Used for CTAs, links, and brand elements
- **Primary Dark**: `#E55A00` - Hover states and emphasis
- **Primary Light**: `#FF8533` - Backgrounds and subtle accents
- **Neutral**: Gray scale from `#F9FAFB` to `#111827`
- **Semantic**: Green (success), <PERSON> (error), Yellow (warning), Blue (info)

### Typography
- **Font Family**: System fonts (-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', etc.)
- **Scale**: From 0.75rem (xs) to 3.75rem (6xl)
- **Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)

## 🏗️ Architecture

### Tech Stack
- **Vue.js 3** - Progressive JavaScript framework
- **Vue Router 4** - Client-side routing
- **Pinia** - State management
- **Tailwind CSS** - Utility-first CSS framework
- **Frappe REST API** - Backend integration

### Project Structure
```
vue-app/
├── components/          # Reusable UI components
│   ├── FilterSidebar.vue
│   ├── ImageUploader.vue
│   ├── ListingCard.vue
│   ├── ListingPreview.vue
│   ├── Pagination.vue
│   └── UserListingCard.vue
├── pages/              # Route components
│   ├── AdminDashboard.vue
│   ├── Dashboard.vue
│   ├── Home.vue
│   ├── Listings.vue
│   ├── Login.vue
│   ├── PostListing.vue
│   └── Register.vue
├── router/             # Routing configuration
│   └── routes.js
├── services/           # API integration
│   └── api.js
├── store/              # State management
│   ├── listings.js
│   ├── user.js
│   └── wishlist.js
├── App.vue             # Root component
├── main.js             # Application entry point
└── style.css           # Global styles
```

## 🚀 Features

### Public Features
- **Homepage**: Hero section with search, featured listings, and stats
- **Browse Listings**: Grid/list view with advanced filtering and sorting
- **Listing Details**: Comprehensive listing view with image gallery
- **Search**: Real-time search with suggestions and filters
- **Categories**: Browse by category with subcategory support

### User Features
- **Authentication**: Login, register, password reset
- **Dashboard**: Personal dashboard with statistics and listing management
- **Post Listings**: Multi-step form with image upload and preview
- **My Listings**: Manage drafts, pending, published, and sold listings
- **Wishlist**: Save and manage favorite listings
- **Messages**: Communication with other users
- **Profile**: Account settings and preferences

### Admin Features
- **Admin Dashboard**: Overview with key metrics and notifications
- **Listing Approval**: Review and approve/reject pending listings
- **Bulk Operations**: Approve or reject multiple listings at once
- **User Management**: View and manage user accounts
- **Reports**: Analytics and platform insights

## 🔧 API Integration

### Service Layer
The `api.js` service provides centralized API communication:

```javascript
// Example usage
const response = await window.apiService.getListings(filters, page, pageSize)
const listing = await window.apiService.getListing(listingId)
await window.apiService.createListing(listingData)
```

### Available API Methods
- **Listings**: CRUD operations, search, filtering
- **Categories**: Fetch categories and category tree
- **Users**: Profile management, authentication
- **Wishlist**: Add/remove listings from wishlist
- **Messages**: Conversation management
- **File Upload**: Image and document upload

## 🎯 State Management

### Stores
- **User Store**: Authentication, profile, permissions
- **Listings Store**: Listing data, filters, pagination
- **Wishlist Store**: Saved listings management
- **Categories Store**: Category data and hierarchy

### Store Usage
```javascript
import { useUserStore } from './store/user.js'
import { useListingsStore } from './store/listings.js'

const userStore = useUserStore()
const listingsStore = useListingsStore()

// Check authentication
if (userStore.isLoggedIn) {
  // User is authenticated
}

// Fetch listings
await listingsStore.fetchListings(filters, page)
```

## 🎨 UI Components

### Design Principles
- **Responsive**: Mobile-first design with breakpoints
- **Accessible**: WCAG AA compliance with proper contrast and focus states
- **Consistent**: Unified spacing, typography, and color usage
- **Professional**: Clean, modern interface with subtle animations

### Key Components
- **ListingCard**: Displays listing information in grid/list format
- **ImageUploader**: Drag-and-drop image upload with preview
- **FilterSidebar**: Advanced filtering interface
- **UserListingCard**: User's listing management interface
- **ListingPreview**: Preview component for listing creation

## 🔐 Authentication & Authorization

### Route Guards
- **requireAuth**: Redirects to login if not authenticated
- **requireAdmin**: Checks for admin role
- **redirectIfAuth**: Redirects authenticated users away from auth pages

### Permission Levels
- **Guest**: Browse listings, view details
- **User**: All guest features + post listings, manage account
- **Admin**: All user features + approve listings, manage users

## 📱 Responsive Design

### Breakpoints
- **sm**: 640px and up
- **md**: 768px and up
- **lg**: 1024px and up
- **xl**: 1280px and up

### Mobile Optimizations
- Touch-friendly interface elements
- Collapsible navigation menu
- Optimized image loading
- Swipe gestures for image galleries

## 🚀 Performance

### Optimizations
- **Lazy Loading**: Images and route components
- **Code Splitting**: Dynamic imports for non-critical routes
- **Caching**: API response caching where appropriate
- **Debouncing**: Search and filter inputs
- **Virtual Scrolling**: For large lists (planned)

## 🔧 Development

### Setup
1. The Vue.js app is integrated within the Frappe dmsoko app
2. No separate build process required - uses CDN libraries
3. Files are served directly from the Frappe static file system

### File Organization
- Components are organized by feature and reusability
- Pages correspond to main application routes
- Stores manage global application state
- Services handle external API communication

### Coding Standards
- Vue 3 Composition API
- ES6+ JavaScript features
- Consistent naming conventions
- Comprehensive error handling
- Responsive design patterns

## 🎯 Future Enhancements

### Planned Features
- Real-time messaging with WebSocket
- Advanced image editing and cropping
- Social login integration (Google, Facebook)
- Progressive Web App (PWA) capabilities
- Advanced analytics and reporting
- Multi-language support
- Dark mode theme

### Technical Improvements
- TypeScript migration
- Unit and integration testing
- Performance monitoring
- SEO optimization
- Accessibility improvements

## 📊 Browser Support

### Supported Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Polyfills
- ES6+ features are used with modern browser support
- Fallbacks provided for older browsers where necessary

## 🤝 Contributing

### Code Style
- Use Vue 3 Composition API
- Follow Tailwind CSS utility patterns
- Maintain consistent component structure
- Include proper error handling
- Write descriptive commit messages

### Component Guidelines
- Keep components focused and reusable
- Use props for data input, events for output
- Include proper TypeScript-style prop validation
- Follow accessibility best practices
- Optimize for performance

---

Built with ❤️ for the DMSoko platform using Vue.js 3 and modern web technologies.
